"use client";
import React from "react";
import { motion } from "framer-motion";
import { Clock, CheckCircle, AlertCircle, Loader2 } from "lucide-react";

interface InterviewPreparationOverlayProps {
  isVisible: boolean;
  progress: number; // 0-100
  currentStep: string;
  totalQuestions: number;
  loadedQuestions: number;
  failedQuestions: number;
  estimatedTimeRemaining?: number; // in seconds
}

const InterviewPreparationOverlay: React.FC<InterviewPreparationOverlayProps> = ({
  isVisible,
  progress,
  currentStep,
  totalQuestions,
  loadedQuestions,
  failedQuestions,
  estimatedTimeRemaining,
}) => {
  if (!isVisible) return null;

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getProgressColor = () => {
    if (failedQuestions > 0) return "from-orange-500 to-red-500";
    if (progress < 30) return "from-blue-500 to-indigo-600";
    if (progress < 70) return "from-indigo-500 to-purple-600";
    return "from-purple-500 to-green-500";
  };

  const getStatusIcon = () => {
    if (failedQuestions > 0) {
      return <AlertCircle className="w-12 h-12 text-orange-500 mx-auto mb-4" />;
    }
    if (progress >= 100) {
      return <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />;
    }
    return <Clock className="w-12 h-12 animate-spin text-indigo-600 mx-auto mb-4" />;
  };

  const getStatusMessage = () => {
    if (progress >= 100) {
      return "Interview Ready!";
    }
    if (failedQuestions > 0) {
      return "Preparing Interview (Some Issues)";
    }
    return "Preparing Your Interview";
  };

  const getStatusDescription = () => {
    if (progress >= 100) {
      return "All questions have been prepared. Starting your interview now...";
    }
    if (failedQuestions > 0) {
      return `Loading questions for optimal experience. ${failedQuestions} question${failedQuestions > 1 ? 's' : ''} may load during the interview.`;
    }
    return "Pre-loading questions in the background for a smooth interview experience...";
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 text-center shadow-2xl mx-4 max-w-md w-full"
      >
        {/* Status Icon */}
        {getStatusIcon()}

        {/* Title */}
        <h3 className="text-xl font-bold text-gray-800 mb-2">
          {getStatusMessage()}
        </h3>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-6">
          {getStatusDescription()}
        </p>

        {/* Progress Section */}
        <div className="mb-6">
          {/* Progress Bar */}
          <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden mb-3">
            <motion.div
              className={`h-full bg-gradient-to-r ${getProgressColor()}`}
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(progress, 100)}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>

          {/* Progress Text */}
          <div className="flex justify-between items-center text-sm text-gray-600 mb-2">
            <span>{Math.round(progress)}% complete</span>
            <span>{loadedQuestions}/{totalQuestions} questions ready</span>
          </div>

          {/* Current Step */}
          {currentStep && (
            <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mb-2">
              <Loader2 className="w-3 h-3 animate-spin" />
              <span>{currentStep}</span>
            </div>
          )}

          {/* Time Remaining */}
          {estimatedTimeRemaining && estimatedTimeRemaining > 0 && (
            <p className="text-xs text-gray-500">
              Estimated time remaining: {formatTime(estimatedTimeRemaining)}
            </p>
          )}
        </div>

        {/* Status Details */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="bg-green-50 rounded-lg p-3">
            <div className="flex items-center justify-center mb-1">
              <CheckCircle className="w-4 h-4 text-green-500" />
            </div>
            <div className="text-lg font-semibold text-green-700">{loadedQuestions}</div>
            <div className="text-xs text-green-600">Ready</div>
          </div>

          <div className="bg-blue-50 rounded-lg p-3">
            <div className="flex items-center justify-center mb-1">
              <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
            </div>
            <div className="text-lg font-semibold text-blue-700">
              {Math.max(0, totalQuestions - loadedQuestions - failedQuestions)}
            </div>
            <div className="text-xs text-blue-600">Loading</div>
          </div>

          {failedQuestions > 0 && (
            <div className="bg-orange-50 rounded-lg p-3">
              <div className="flex items-center justify-center mb-1">
                <AlertCircle className="w-4 h-4 text-orange-500" />
              </div>
              <div className="text-lg font-semibold text-orange-700">{failedQuestions}</div>
              <div className="text-xs text-orange-600">Pending</div>
            </div>
          )}
        </div>

        {/* Tips */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-xs text-blue-700 font-medium mb-1">💡 Pro Tip</p>
          <p className="text-xs text-blue-600">
            Questions are being prepared in the background. This ensures smooth transitions 
            between questions during your interview.
          </p>
        </div>

        {/* Loading Animation */}
        <div className="mt-4 flex justify-center">
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-indigo-500 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default InterviewPreparationOverlay;
